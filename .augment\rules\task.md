---
type: "always_apply"
---

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Always use (activate) it when doing python development. First, to check whether 'uv' is available, use `which uv`. If that's the case, first activate the venv, and then use `uv pip install` to install packages. Otherwise, fall back to `pip`.
- After writing the test code and ensuring all functions are correct, you should delete the test code files to avoid cluttering the project. Meanwhile, all test files should start with "Test_".
- No matter whether the user inputs in Chinese or English, you should default to replying in Chinese, unless the user requests you to reply in English.
- Please note that it is currently July 2025.
- Be sure to use an empty card for inference; before running memory-intensive programs, check the memory usage with nvidia-smi to avoid conflicts with other programs.

## augment learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- When adding new items to a grid layout, check for CSS rules with `:nth-child()` selectors that may limit styling to only the first N items. These selectors can cause layout issues for items beyond the specified range. Always ensure grid layout rules apply to all items consistently.

# Scratchpad

## 当前任务：添加新的项目展示卡片 - Embodied-R

### 任务分析：
用户要求在现有网页项目中添加一个新的项目展示卡片，具体要求：
1. 使用 `assets/images/embodied-R.png` 作为项目图片 ✓ (文件已存在)
2. 设置项目链接为 `https://embodiedcity.github.io/Embodied-R/`
3. 文案内容需要与现有项目保持一致的写作风格和语调
4. 配色方案必须与网页整体色系保持一致
5. 布局样式要与现有项目卡片统一
6. 将新项目卡片添加到现有的项目展示区域

### 现有项目卡片分析：
从 index.html 分析得出现有项目卡片的结构：
- 项目卡片位于 `<section class="section project">` 内的 `<ul class="grid-list">` 中
- 每个项目都是一个 `<li>` 元素，包含 `project-card` div
- 卡片有两种样式：`project-card-1` 和 `project-card-2`，交替使用
- 每个卡片包含：
  - `card-content` 区域：包含 tag、title、description、链接
  - `card-banner` 区域：包含项目图片
- 配色方案有多种：紫色系、蓝色系、绿色系、粉色系

### 执行计划：
[x] 分析现有项目的文案风格和内容结构
[x] 为 Embodied-R 项目编写合适的文案内容
[x] 选择合适的配色方案（避免与现有项目重复）
[x] 在项目列表中添加新的项目卡片
[x] 确保响应式设计和代码一致性

### 实施详情：
- **配色方案**：选择橙色系 (`#fff8f0` 背景，`#e67e22` 文字) 来补充现有的紫、蓝、绿、粉色系
- **项目标签**：使用 "Framework" 标签，突出其作为框架的特性
- **文案风格**：保持与现有项目一致的学术性和专业性描述
- **布局样式**：使用 `project-card-1` 样式，与现有项目交替排列
- **图片设置**：使用指定的 `embodied-R.png` 图片，设置合适的尺寸和alt文本

### 发现的布局问题：
用户反馈新添加的项目卡片出现布局问题，内容挤压到左侧。

### 问题分析：
- CSS中的响应式布局规则在 `@media (min-width: 768px)` 媒体查询中定义
- 基础样式设置 `flex-direction: column`，大屏幕时改为 `flex-direction: row`
- 内容区域应占33.33%，图片区域应占66.66%
- 需要检查CSS特异性和媒体查询应用情况

### 修复计划：
[x] 检查CSS媒体查询是否正确应用
[x] 验证HTML结构与其他正常工作的卡片一致
[x] 确保CSS选择器特异性正确 - 添加了 !important 规则
[ ] 测试不同屏幕尺寸下的显示效果

### 修复尝试：
1. **CSS特异性问题**：在 `flex-direction: row` 规则中添加了 `!important` 来确保样式被正确应用
2. **HTML结构验证**：确认HTML结构与其他正常工作的项目卡片完全一致
3. **媒体查询检查**：确认响应式规则在 `@media (min-width: 768px)` 中正确定义
4. **选择器优先级提升**：将选择器改为更具体的 `.project .project-card-1` 形式
5. **宽度规则强化**：为内容和图片区域的宽度规则添加 `!important`

### 根本问题发现和修复：
[x] **发现根本原因**：CSS中存在 `.project .grid-list>li:nth-child(-n+4)` 选择器
   - 该规则只对前4个项目卡片应用 `grid-column: 1 / 3`（跨越两列）
   - 第5个及以后的项目卡片没有此规则，只占用1列，导致布局异常
[x] **修复方案**：将选择器从 `:nth-child(-n+4)` 改为通用选择器
   - 修改前：`.project .grid-list>li:nth-child(-n+4) { grid-column: 1 / 3; }`
   - 修改后：`.project .grid-list>li { grid-column: 1 / 3; }`
   - 确保所有项目卡片都能跨越两列显示
