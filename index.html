<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- 
    - primary meta tags
  -->
  <title>EmbodiedCity</title>
  <meta name="title" content="EmbodiedCity">
  <meta name="description" content="Where urban life meets embodied intelligence">

  <!-- 
    - favicon
  -->
  <link rel="shortcut icon" href="./favicon.svg" type="image/svg+xml">

  <!-- 
    - google font link
  -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@500;700&display=swap" rel="stylesheet">

  <!-- 
    - custom css link
  -->
  <link rel="stylesheet" href="./assets/css/style.css">

  <!-- 
    - preload images
  -->
  <link rel="preload" as="image" href="./assets/images/main_web.svg">
  <link rel="preload" as="image" href="./assets/images/Blog.svg">

</head>

<body>

  <!-- 
    - #HEADER
  -->

  <header class="header" data-header>
    <div class="container">

      <a id="Home-section" class="logo">
        <img src="./assets/images/logo-dark.svg" width="245" height="36" alt="EmbodiedCity">
      </a>

      <nav class="navbar" data-navbar>

        <div class="navbar-top">
          <a class="logo">
            <img src="./assets/images/1.gif" width="128" height="48" alt="EmbodiedCity">
          </a>

          <button class="nav-close-btn" aria-label="close menu" data-nav-toggler>
            <ion-icon name="close-outline" aria-hidden="true"></ion-icon>
          </button>
        </div>

        <ul class="navbar-list">

          <li>
            <a href="#" class="navbar-link">Home</a>
          </li>

          <li>
            <a href="#work-section" class="navbar-link">Work</a>
          </li>

          <li>
            <a href="#workshop-section" class="navbar-link">WorkShop</a>
          </li>
          <!-- 
          <li>
            <a href="#member-section" class="navbar-link">Member</a>
          </li>

          <li>
            <a href="#contact-section" class="navbar-link">Contact</a>
          </li> -->

        </ul>

        <div class="wrapper">
          <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
        </div>

      </nav>

    </div>
  </header>


  <main>
    <article>

      <section class="section hero" aria-label="home">
        <div class="container">

          <figure class="hero-banner">
            <img src="./assets/images/1.gif" width="660" height="540" alt="EmbodiedCity" data-reveal="top">

            <!--            <img src="./assets/images/hero-shape.svg" width="203" height="91" alt="250+ Projects Done" class="shape"-->
            <!--              data-reveal="top" data-reveal-delay="0.25s">-->
          </figure>

          <div class="hero-content">

            <h1 class="h1 hero-title" data-reveal="top" data-reveal-delay="0.5s">
              Where urban life meets embodied intelligence
            </h1>

            <p class="section-text" data-reveal="top" data-reveal-delay="0.75s">
              Tsinghua University EmbodiedCity Team
            </p>

            <div class="btn-wrapper" data-reveal="top" data-reveal-delay="1s">
              <a href="#work-section" class="btn btn-primary">See Our Works</a>

              <a href="#contact-section" class="btn btn-secondary">Contact us</a>
            </div>

          </div>

        </div>
      </section>





      <!-- 
        - #PROJECT
      -->

      <section class="section project" aria-labelledby="project-label">
        <div class="container">

          <div class="title-wrapper" data-reveal="top">

            <div>
              <h2 class="h2 section-title" id="work-section">Latest Projects</h2>



              <p class="section-text">
                Check out some of my latest projects with creative works.
              </p>
            </div>

            <a href="#" class="btn btn-secondary">See All Projects</a>

          </div>

          <ul class="grid-list">

            <li>
              <div class="project-card project-card-1" style="background-color: #f8f5fb">

                <div class="card-content" data-reveal="left">
                  <p class="card-tag" style="color: #a07cc5">Benchmark</p>
                  <h3 class="h3 card-title">Embodied City</h3>
                  <p class="card-text">
                    We release a new benchmark platform, named embodied-city, for embodied intelligence in urban
                    environments.
                  </p>
                  <a href="https://embodied-city.fiblab.net/" class="btn-text" style="color: #a07cc5">
                    <span class="span">See Project</span>

                    <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                  </a>
                </div>

                <figure class="card-banner" data-reveal="right">
                  <img src="./assets/images/project-1.png" width="650" height="300" loading="lazy" alt="Web Design"
                    class="w-100">
                </figure>

              </div>
            </li>


            <li>
              <div class="project-card project-card-2" style="background-color: #f1f5fd">

                <div class="card-content" data-reveal="right">

                  <p class=" card-tag" style="color: #3f78e0">Benchmark</p>

                  <h3 class="h3 card-title">UrbanVideo-Bench</h3>

                  <p class="card-text">
                    We introduce a benchmark to evaluate whether video-large language models (Video-LLMs) can naturally
                    process continuous first-person visual observations
                    like humans, enabling recall, perception, reasoning, and navigation.
                  </p>

                  <a href="https://embodiedcity.github.io/UrbanVideo-Bench/" class="btn-text" style="color: #3f78e0">
                    <span class="span">See Project</span>

                    <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                  </a>

                </div>

                <figure class="card-banner" data-reveal="left">
                  <img src="./assets/images/3.png" width="400" height="67" loading="lazy" alt="Web Design"
                    class="w-100">
                </figure>

              </div>
            </li>

            <li>
              <div class="project-card project-card-1" style="background-color: #f5faf7">

                <div class="card-content" data-reveal="left">
                  <p class="card-tag" style="color:#7cb798">Benchmark</p>
                  <h3 class="h3 card-title">Open3DVQA</h3>
                  <p class="card-text">
                    Open3DVQA is a benchamrk to comprehensively evaluate the spatial reasoning capacities of current
                    SOTA foundation models in open 3D space.
                    It consists of 9k VOA samples, collected using aneffcient semi-automated tool in a high-fdelity
                    urban simulator.
                  </p>
                  <a href="https://github.com/WeichenZh/Open3DVQA" class="btn-text" style="color: #7cb798">
                    <span class="span">See Project</span>

                    <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                  </a>
                </div>

                <figure class="card-banner" data-reveal="right">
                  <img src="./assets/images/project-2.png" width="650" height="300" loading="lazy" alt="Web Design"
                    class="w-100">
                </figure>

              </div>
            </li>



            
            <li>
              <div class="project-card project-card-2" style="background-color: #fcf4f6">

                <div class="card-content" data-reveal="right">

                  <p class=" card-tag" style="color: #d16b86">Task</p>

                  <h3 class="h3 card-title">CityEQA</h3>

                  <p class="card-text">
                    We introduce CityEQA, a new task where an embodied agent answers open-vocabulary questions through
                    active exploration in dynamic city spaces.
                  </p>

                  <a href="https://arxiv.org/pdf/2502.12532" class="btn-text" style="color: #d16b86">
                    <span class="span">See Project</span>

                    <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                  </a>

                </div>

                <figure class="card-banner" data-reveal="left">
                  <img src="./assets/images/5.png" width="400" height="67" loading="lazy" alt="Web Design"
                    class="w-100">
                </figure>

              </div>
            </li>

            <li>
              <div class="project-card project-card-1" style="background-color: #fff8f0">

                <div class="card-content" data-reveal="left">
                  <p class="card-tag" style="color: #e67e22">Framework</p>
                  <h3 class="h3 card-title">Embodied-R</h3>
                  <p class="card-text">
                    A comprehensive framework for embodied reasoning in urban environments, enabling agents to perform
                    complex spatial reasoning and decision-making tasks through multi-modal perception and interaction.
                  </p>
                  <a href="https://embodiedcity.github.io/Embodied-R/" class="btn-text" style="color: #e67e22">
                    <span class="span">See Project</span>

                    <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                  </a>
                </div>

                <figure class="card-banner" data-reveal="right">
                  <img src="./assets/images/embodied-R.png" width="650" height="300" loading="lazy"
                    alt="Embodied-R Framework" class="w-100">
                </figure>

              </div>
            </li>
          </ul>

        </div>
      </section>

      <section class="section project" aria-labelledby="project-label">
        <div class="container">

          <div class="title-wrapper" data-reveal="top">

            <div>
              <h2 class="h2 section-title" id="workshop-section">Workshop</h2>

            </div>

          </div>

          <ul class="grid-list">

            <li>
              <div class="project-card project-card-1" style="background-color: #f8f5fb">

                <div class="">
                  <p class="card-tag" style="color: #a07cc5">Conference</p>
                  <h3 class="h3 card-title" href="https://embodiedcity.github.io/iclr25-workshop">ICLR 2025 Workshop
                  </h3>
                  <p class="">
                    This workshop is motivated by a fact: human beings have strong embodied intelligence in an open
                    environment, but it is still challenging for large language models and LLM agents. Depsite some
                    progresses on embodied AI on static and indoor environment, the LLM agents are still struggling in
                    tasks in large-scale outdoor environment, such as navigation, search, spatial reasoning, task
                    planning, etc. Therefore, we propose this workshop to discuss the recent advances on the related
                    research area and looking forward to the future development. Specifically, it delves into topics of
                    outdoor embodied intelligence, such as spatial intelligence and embodied perception, reasoning and
                    planning, decision-making and action, multi-agent and human-agent collaboration, and the development
                    of simulators, testbeds, datasets, and benchmarks. This comprehensive exploration of embodied LLM
                    agents in open city environment holds the potential to advance the field of artificial intelligence
                    and open up new applications in various domains.We also have a special poster/short paper session
                    for those solutions that perform best in the Open Urban Environment Embodied Intelligence
                    Competition.

                    <a href="https://embodiedcity.github.io/iclr25-workshop" class="btn-text" style="color: #a07cc5">
                      <span class="span">See Project</span>

                      <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                    </a>
                    <br>
                    <br>
                </div>
              </div>
            </li>
          </ul>

        </div>
      </section>



    </article>
  </main>





  <!-- 
    - #FOOTER
  -->

  <footer class="footer">
    <div class="container">

      <p class="copyright">
        © 2025 Tsinghua University EmbodiedCity Team
      </p>

      <ul class="social-list">

        <li>
          <a href="#" class="social-link">
            <ion-icon name="logo-twitter"></ion-icon>
          </a>
        </li>

        <li>
          <a href="#" class="social-link">
            <ion-icon name="logo-facebook"></ion-icon>
          </a>
        </li>

        <li>
          <a href="#" class="social-link">
            <ion-icon name="logo-dribbble"></ion-icon>
          </a>
        </li>

        <li>
          <a href="#" class="social-link">
            <ion-icon name="logo-instagram"></ion-icon>
          </a>
        </li>

        <li>
          <a href="#" class="social-link">
            <ion-icon name="logo-youtube"></ion-icon>
          </a>
        </li>

      </ul>

    </div>
  </footer>





  <!-- 
    - custom js link
  -->
  <script src="./assets/js/script.js"></script>

  <!-- 
    - ionicon link
  -->
  <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
  <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>

</body>

</html>